"""
Application metrics and monitoring
"""

import time
import logging
from typing import Dict, Any, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class MetricsCollector:
    """Simple metrics collector for monitoring application performance"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.request_times = deque(maxlen=max_history)
        self.request_counts = defaultdict(int)
        self.error_counts = defaultdict(int)
        self.processing_times = deque(maxlen=max_history)
        self.token_usage = deque(maxlen=max_history)
        self.start_time = datetime.utcnow()
    
    def record_request(self, endpoint: str, method: str, duration: float, status_code: int):
        """Record a request"""
        self.request_times.append({
            'timestamp': datetime.utcnow(),
            'endpoint': endpoint,
            'method': method,
            'duration': duration,
            'status_code': status_code
        })
        
        self.request_counts[f"{method} {endpoint}"] += 1
        
        if status_code >= 400:
            self.error_counts[f"{status_code}"] += 1
    
    def record_processing(self, duration: float, images_processed: int, input_tokens: int, output_tokens: int):
        """Record processing metrics"""
        self.processing_times.append({
            'timestamp': datetime.utcnow(),
            'duration': duration,
            'images_processed': images_processed
        })
        
        self.token_usage.append({
            'timestamp': datetime.utcnow(),
            'input_tokens': input_tokens,
            'output_tokens': output_tokens
        })
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics"""
        now = datetime.utcnow()
        uptime = now - self.start_time
        
        # Calculate averages for recent requests
        recent_requests = [r for r in self.request_times if now - r['timestamp'] < timedelta(minutes=5)]
        avg_response_time = sum(r['duration'] for r in recent_requests) / len(recent_requests) if recent_requests else 0
        
        # Calculate processing metrics
        recent_processing = [p for p in self.processing_times if now - p['timestamp'] < timedelta(minutes=5)]
        avg_processing_time = sum(p['duration'] for p in recent_processing) / len(recent_processing) if recent_processing else 0
        total_images_processed = sum(p['images_processed'] for p in self.processing_times)
        
        # Calculate token usage
        recent_tokens = [t for t in self.token_usage if now - t['timestamp'] < timedelta(minutes=5)]
        total_input_tokens = sum(t['input_tokens'] for t in self.token_usage)
        total_output_tokens = sum(t['output_tokens'] for t in self.token_usage)
        
        return {
            'uptime_seconds': uptime.total_seconds(),
            'total_requests': len(self.request_times),
            'request_counts': dict(self.request_counts),
            'error_counts': dict(self.error_counts),
            'avg_response_time_5min': avg_response_time,
            'avg_processing_time_5min': avg_processing_time,
            'total_images_processed': total_images_processed,
            'total_input_tokens': total_input_tokens,
            'total_output_tokens': total_output_tokens,
            'recent_requests_5min': len(recent_requests),
            'recent_processing_5min': len(recent_processing)
        }

# Global metrics collector instance
metrics_collector = MetricsCollector()

def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector"""
    return metrics_collector
