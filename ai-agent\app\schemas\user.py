"""
User Pydantic schemas
"""

from pydantic import BaseModel, EmailStr
from typing import Optional
from app.schemas.base import BaseSchema, BaseCreateSchema

class UserBase(BaseModel):
    """Base user schema"""
    username: str
    email: EmailStr
    is_active: bool = True
    is_superuser: bool = False

class UserCreate(BaseCreateSchema):
    """User creation schema"""
    username: str
    email: EmailStr
    password: str

class UserUpdate(BaseModel):
    """User update schema"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None

class UserResponse(BaseSchema, UserBase):
    """User response schema"""
    pass

class UserInDB(UserBase):
    """User in database schema"""
    id: str
    hashed_password: str
