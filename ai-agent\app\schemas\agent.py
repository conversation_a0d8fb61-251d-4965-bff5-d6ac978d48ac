"""
Agent Pydantic schemas
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from app.schemas.base import BaseSchema, BaseCreateSchema

class AgentBase(BaseModel):
    """Base agent schema"""
    name: str
    description: Optional[str] = None
    model_name: str
    is_active: bool = True

    model_config = {"protected_namespaces": ()}

class AgentCreate(BaseCreateSchema):
    """Agent creation schema"""
    name: str
    description: Optional[str] = None
    model_name: str
    configuration: Optional[Dict[str, Any]] = None
    capabilities: Optional[List[str]] = None
    supported_file_types: Optional[List[str]] = None

    model_config = {"protected_namespaces": ()}

class AgentUpdate(BaseModel):
    """Agent update schema"""
    name: Optional[str] = None
    description: Optional[str] = None
    model_name: Optional[str] = None
    is_active: Optional[bool] = None
    configuration: Optional[Dict[str, Any]] = None

    model_config = {"protected_namespaces": ()}

class AgentResponse(BaseSchema, AgentBase):
    """Agent response schema"""
    configuration: Optional[Dict[str, Any]] = None
    capabilities: Optional[List[str]] = None
    supported_file_types: Optional[List[str]] = None

# Processing schemas
class ProcessingRequest(BaseModel):
    """Processing request schema"""
    chunk_size: Optional[int] = 20

class ProcessingResult(BaseModel):
    """Processing result schema"""
    success: bool
    output_file_path: Optional[str] = None
    input_tokens: int = 0
    output_tokens: int = 0
    processing_time: float = 0.0
    images_processed: int = 0
    error_message: Optional[str] = None

class ProcessingResponse(BaseModel):
    """Processing response schema"""
    message: str
    file_url: Optional[str] = None
    processing_stats: Optional[Dict[str, Any]] = None
