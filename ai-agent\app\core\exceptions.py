"""
Custom exceptions and exception handlers
"""

import logging
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

logger = logging.getLogger(__name__)

class ImageProcessingError(Exception):
    """Custom exception for image processing errors"""
    def __init__(self, message: str, details: str = None):
        self.message = message
        self.details = details
        super().__init__(self.message)

class AIServiceError(Exception):
    """Custom exception for AI service errors"""
    def __init__(self, message: str, service: str = None):
        self.message = message
        self.service = service
        super().__init__(self.message)

class FileProcessingError(Exception):
    """Custom exception for file processing errors"""
    def __init__(self, message: str, filename: str = None):
        self.message = message
        self.filename = filename
        super().__init__(self.message)

async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions"""
    logger.error(f"HTTP exception: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "HTTP Exception",
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle validation exceptions"""
    logger.error(f"Validation error: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={
            "error": "Validation Error",
            "message": "Request validation failed",
            "details": exc.errors()
        }
    )

async def image_processing_exception_handler(request: Request, exc: ImageProcessingError):
    """Handle image processing exceptions"""
    logger.error(f"Image processing error: {exc.message}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Image Processing Error",
            "message": exc.message,
            "details": exc.details
        }
    )

async def ai_service_exception_handler(request: Request, exc: AIServiceError):
    """Handle AI service exceptions"""
    logger.error(f"AI service error: {exc.message}")
    return JSONResponse(
        status_code=503,
        content={
            "error": "AI Service Error",
            "message": exc.message,
            "service": exc.service
        }
    )

async def file_processing_exception_handler(request: Request, exc: FileProcessingError):
    """Handle file processing exceptions"""
    logger.error(f"File processing error: {exc.message}")
    return JSONResponse(
        status_code=400,
        content={
            "error": "File Processing Error",
            "message": exc.message,
            "filename": exc.filename
        }
    )

async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error(f"Unexpected error: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "An unexpected error occurred"
        }
    )

def setup_exception_handlers(app: FastAPI):
    """Setup all exception handlers"""
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(ImageProcessingError, image_processing_exception_handler)
    app.add_exception_handler(AIServiceError, ai_service_exception_handler)
    app.add_exception_handler(FileProcessingError, file_processing_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
