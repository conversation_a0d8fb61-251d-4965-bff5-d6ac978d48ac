"""
Message Pydantic schemas
"""

from pydantic import BaseModel
from typing import Optional
from app.schemas.base import BaseSchema, BaseCreateSchema

class MessageBase(BaseModel):
    """Base message schema"""
    content: str
    role: str  # 'user', 'assistant', 'system'

class MessageCreate(BaseCreateSchema):
    """Message creation schema"""
    content: str
    role: str
    conversation_id: str
    sequence_number: int

class MessageUpdate(BaseModel):
    """Message update schema"""
    content: Optional[str] = None

class MessageResponse(BaseSchema, MessageBase):
    """Message response schema"""
    conversation_id: str
    sequence_number: int
    input_tokens: int = 0
    output_tokens: int = 0
