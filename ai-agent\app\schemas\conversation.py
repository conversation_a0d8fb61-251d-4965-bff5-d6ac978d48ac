"""
Conversation Pydantic schemas
"""

from pydantic import BaseModel
from typing import Optional, List
from app.schemas.base import BaseSchema, BaseCreateSchema

class ConversationBase(BaseModel):
    """Base conversation schema"""
    title: str
    description: Optional[str] = None

class ConversationCreate(BaseCreateSchema):
    """Conversation creation schema"""
    title: str
    description: Optional[str] = None
    user_id: str

class ConversationUpdate(BaseModel):
    """Conversation update schema"""
    title: Optional[str] = None
    description: Optional[str] = None

class ConversationResponse(BaseSchema, ConversationBase):
    """Conversation response schema"""
    user_id: str
    message_count: Optional[int] = 0
