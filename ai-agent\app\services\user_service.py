"""
User Service
"""

from typing import Optional
from sqlalchemy.orm import Session
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.services.base import BaseService
from app.core.security import get_password_hash, verify_password

class UserService(BaseService[User, UserCreate, UserUpdate]):
    """Service for user operations"""
    
    def __init__(self, db: Session):
        super().__init__(User, db)
    
    def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        return self.db.query(User).filter(User.email == email).first()
    
    def get_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        return self.db.query(User).filter(User.username == username).first()
    
    def create(self, obj_in: UserCreate) -> User:
        """Create a new user with hashed password"""
        obj_data = obj_in.dict()
        password = obj_data.pop("password")
        obj_data["hashed_password"] = get_password_hash(password)
        db_obj = User(**obj_data)
        self.db.add(db_obj)
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj
    
    def authenticate(self, username: str, password: str) -> Optional[User]:
        """Authenticate user"""
        user = self.get_by_username(username)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user
    
    def is_active(self, user: User) -> bool:
        """Check if user is active"""
        return user.is_active
    
    def is_superuser(self, user: User) -> bool:
        """Check if user is superuser"""
        return user.is_superuser

# Dependency injection
def get_user_service(db: Session) -> UserService:
    """Get user service instance"""
    return UserService(db)
