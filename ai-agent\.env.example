# Environment Configuration Example
# Copy this file to .env and update the values

# Application Settings
PROJECT_NAME="AI Agent - Image Categorization API"
VERSION="2.0.0"
ENVIRONMENT="development"  # development, staging, production
DEBUG=true

# Server Configuration
HOST="127.0.0.1"
PORT=8000

# AI Service Configuration
GEMINI_API_KEY="your_gemini_api_key_here"
GEMINI_MODEL="gemini-2.0-flash"

# File Processing Configuration
MAX_FILE_SIZE=52428800  # 50MB in bytes
MAX_WORKERS=20
DEFAULT_CHUNK_SIZE=20
ALLOWED_FILE_TYPES=[".zip", ".pdf"]

# Image Processing Configuration
IMAGE_MAX_SIZE=1024  # Maximum image dimension
IMAGE_QUALITY=85     # JPEG quality (1-100)

# Database Configuration
DATABASE_URL="sqlite:///./app.db"
# For PostgreSQL: DATABASE_URL="postgresql://user:password@localhost/dbname"

# Security Configuration
SECRET_KEY="your-secret-key-here-change-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM="HS256"

# Logging Configuration
LOG_LEVEL="INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE="logs/app.log"
LOG_ROTATION="1 day"
LOG_RETENTION="30 days"

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080","http://************:5007"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]

# Rate Limiting (if implemented)
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60  # seconds

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_RETENTION_HOURS=24
