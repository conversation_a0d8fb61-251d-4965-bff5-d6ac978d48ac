"""
Agent Service - Main image processing and AI integration
"""

import os
import asyncio
import logging
from typing import <PERSON><PERSON>
from concurrent.futures import ThreadPoolExecutor

from app.schemas.agent import ProcessingResult
from app.config.settings import get_settings
from app.core.exceptions import ImageProcessingError, AIServiceError, FileProcessingError

# Import the main processing logic
from app.core.image_processor import run_agent

logger = logging.getLogger(__name__)
settings = get_settings()

class AgentService:
    """Service for AI agent operations and image processing"""
    
    def __init__(self):
        self.settings = get_settings()
        self.executor = ThreadPoolExecutor(max_workers=self.settings.MAX_WORKERS)
    
    async def process_file(
        self, 
        file_path: str, 
        output_path: str, 
        chunk_size: int = None
    ) -> ProcessingResult:
        """
        Process uploaded file using the AI agent
        
        Args:
            file_path: Path to the uploaded file
            output_path: Path where the output PDF should be saved
            chunk_size: Number of images to process in each batch
            
        Returns:
            ProcessingResult with success status and processing details
        """
        
        if chunk_size is None:
            chunk_size = self.settings.DEFAULT_CHUNK_SIZE
            
        logger.info(f"Starting file processing: {file_path}")
        logger.info(f"Output path: {output_path}")
        logger.info(f"Chunk size: {chunk_size}")
        
        try:
            # Validate input file exists
            if not os.path.exists(file_path):
                raise FileProcessingError(f"Input file not found: {file_path}")
            
            # Validate file type
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.settings.ALLOWED_FILE_TYPES:
                raise FileProcessingError(
                    f"Unsupported file type: {file_ext}. Supported types: {self.settings.ALLOWED_FILE_TYPES}"
                )
            
            # Check API key
            if not self.settings.GEMINI_API_KEY:
                raise AIServiceError("GEMINI_API_KEY not configured", "Gemini Vision API")
            
            # Run the processing in a thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            start_time = asyncio.get_event_loop().time()
            
            # Call the original run_agent function with chunk_size
            generated_pdf_path, input_tokens, output_tokens, chunk_times = await loop.run_in_executor(
                self.executor,
                run_agent,
                file_path,
                output_path,
                chunk_size
            )
            
            end_time = asyncio.get_event_loop().time()
            processing_time = end_time - start_time
            
            # Check if processing was successful
            if generated_pdf_path and os.path.exists(generated_pdf_path):
                logger.info(f"Processing completed successfully")
                logger.info(f"Generated PDF: {generated_pdf_path}")
                logger.info(f"Input tokens: {input_tokens}, Output tokens: {output_tokens}")
                logger.info(f"Processing time: {processing_time:.2f} seconds")
                
                # Count processed images (estimate based on chunk times and chunk size)
                images_processed = len(chunk_times) * chunk_size if chunk_times else 0
                
                return ProcessingResult(
                    success=True,
                    output_file_path=generated_pdf_path,
                    input_tokens=input_tokens or 0,
                    output_tokens=output_tokens or 0,
                    processing_time=processing_time,
                    images_processed=images_processed
                )
            else:
                logger.warning("Processing completed but no PDF was generated")
                return ProcessingResult(
                    success=False,
                    error_message="No images found or could be categorized to generate a PDF"
                )
                
        except FileProcessingError as e:
            logger.error(f"File processing error: {e}")
            return ProcessingResult(
                success=False,
                error_message=str(e)
            )
        except AIServiceError as e:
            logger.error(f"AI service error: {e}")
            return ProcessingResult(
                success=False,
                error_message=f"AI service error: {e}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during processing: {e}")
            return ProcessingResult(
                success=False,
                error_message=f"Unexpected error: {str(e)}"
            )
    
    async def get_agent_capabilities(self) -> dict:
        """Get agent capabilities and configuration"""
        return {
            "name": "Image Categorization Agent",
            "version": "2.0.0",
            "model": self.settings.GEMINI_MODEL,
            "capabilities": [
                "Image categorization",
                "PDF generation",
                "ZIP archive processing",
                "PDF document processing",
                "Batch processing"
            ],
            "supported_file_types": self.settings.ALLOWED_FILE_TYPES,
            "max_file_size": self.settings.MAX_FILE_SIZE,
            "max_workers": self.settings.MAX_WORKERS,
            "image_processing": {
                "max_size": self.settings.IMAGE_MAX_SIZE,
                "quality": self.settings.IMAGE_QUALITY
            },
            "categories": [
                "Address", "Overview", "DC_Disconnect", "Module_label",
                "Inverter_FrontCover", "Inverter_label", "AC_Disconnect",
                "PV_Installation", "Battery", "Permit", "Optimizer_Microinverter"
            ]
        }
    
    async def health_check(self) -> dict:
        """Check agent health and dependencies"""
        return {
            "status": "healthy",
            "api_key_configured": bool(self.settings.GEMINI_API_KEY),
            "model": self.settings.GEMINI_MODEL,
            "max_workers": self.settings.MAX_WORKERS,
            "dependencies": {
                "gemini_api": "available" if self.settings.GEMINI_API_KEY else "not_configured",
                "image_processing": "available",
                "pdf_generation": "available"
            }
        }

# Dependency injection
def get_agent_service() -> AgentService:
    """Get agent service instance"""
    return AgentService()
