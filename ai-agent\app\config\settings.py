"""
Application Settings and Configuration
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import field_validator
from functools import lru_cache

class Settings(BaseSettings):
    """Application settings"""
    
    # Project Information
    PROJECT_NAME: str = "Image Categorization and PDF Generation API"
    PROJECT_DESCRIPTION: str = "AI-powered image categorization and PDF generation service with Gemini Vision integration"
    VERSION: str = "2.0.0"
    
    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # Server Configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # API Configuration
    API_V1_STR: str = "/api/v1"
    ALLOWED_HOSTS: List[str] = ["************:5007","","************:5007","************:5007:8000","http://localhost:8000","http://************:8000","http://************:8000",]
    
    # Security
    SECRET_KEY: str = "your-secret-key-here"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # AI/LLM Configuration
    GEMINI_API_KEY: Optional[str] = None
    GEMINI_MODEL: str = "gemini-2.0-flash"
    
    # File Processing
    MAX_FILE_SIZE: int = 500 * 1024 * 1024  # 500MB
    ALLOWED_FILE_TYPES: List[str] = [".zip", ".pdf", ".jpg", ".jpeg", ".png"]
    TEMP_DIR: str = "temp"
    
    # Processing Configuration
    MAX_WORKERS: int = 20
    DEFAULT_CHUNK_SIZE: int = 20
    IMAGE_MAX_SIZE: int = 1024
    IMAGE_QUALITY: int = 85
    
    # Database (for future use)
    DATABASE_URL: Optional[str] = None
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Security Configuration
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALGORITHM: str = "HS256"

    # Logging Configuration
    LOG_FILE: str = "logs/app.log"
    LOG_ROTATION: str = "1 day"
    LOG_RETENTION: str = "30 days"

    # CORS Configuration
    CORS_ORIGINS: List[str] = ["************:5007","************:5007:8000","************:5007","************:5007:8000","http://localhost:8000","http://************:8000","http://************:8000"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["*"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]

    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_PERIOD: int = 60

    # Monitoring
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    METRICS_RETENTION_HOURS: int = 24
    
    @field_validator("GEMINI_API_KEY", mode='before')
    @classmethod
    def validate_gemini_api_key(cls, v):
        # Allow empty API key for testing/development
        if v is None:
            return ""
        return v

    @field_validator("ENVIRONMENT")
    @classmethod
    def validate_environment(cls, v):
        if v not in ["development", "staging", "production"]:
            raise ValueError("ENVIRONMENT must be one of: development, staging, production")
        return v

    @field_validator('CORS_ORIGINS', mode='before')
    @classmethod
    def validate_cors_origins(cls, v):
        if isinstance(v, str):
            # Handle JSON string format
            import json
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                # Handle comma-separated format
                return [origin.strip() for origin in v.split(',')]
        return v

    @field_validator('CORS_ALLOW_METHODS', mode='before')
    @classmethod
    def validate_cors_methods(cls, v):
        if isinstance(v, str):
            import json
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return [method.strip() for method in v.split(',')]
        return v

    @field_validator('CORS_ALLOW_HEADERS', mode='before')
    @classmethod
    def validate_cors_headers(cls, v):
        if isinstance(v, str):
            import json
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return [header.strip() for header in v.split(',')]
        return v

    model_config = {"env_file": ".env", "case_sensitive": True}

@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()
