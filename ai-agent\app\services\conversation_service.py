"""
Conversation Service
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from app.models.conversation import Conversation
from app.models.message import Message
from app.schemas.conversation import ConversationCreate, ConversationUpdate
from app.services.base import BaseService

class ConversationService(BaseService[Conversation, ConversationCreate, ConversationUpdate]):
    """Service for conversation operations"""
    
    def __init__(self, db: Session):
        super().__init__(Conversation, db)
    
    def get_by_user(self, user_id: str, skip: int = 0, limit: int = 100) -> List[Conversation]:
        """Get conversations by user ID"""
        return (
            self.db.query(Conversation)
            .filter(Conversation.user_id == user_id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_with_messages(self, conversation_id: str) -> Optional[Conversation]:
        """Get conversation with messages"""
        return (
            self.db.query(Conversation)
            .filter(Conversation.id == conversation_id)
            .first()
        )
    
    def add_message(self, conversation_id: str, content: str, role: str) -> Message:
        """Add a message to a conversation"""
        # Get the next sequence number
        last_message = (
            self.db.query(Message)
            .filter(Message.conversation_id == conversation_id)
            .order_by(Message.sequence_number.desc())
            .first()
        )
        
        sequence_number = (last_message.sequence_number + 1) if last_message else 1
        
        message = Message(
            content=content,
            role=role,
            conversation_id=conversation_id,
            sequence_number=sequence_number
        )
        
        self.db.add(message)
        self.db.commit()
        self.db.refresh(message)
        return message

# Dependency injection
def get_conversation_service(db: Session) -> ConversationService:
    """Get conversation service instance"""
    return ConversationService(db)
