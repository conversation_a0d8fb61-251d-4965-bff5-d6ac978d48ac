# AI Agent - Image Categorization and PDF Generation API

A professional FastAPI-based application for AI-powered image categorization and PDF report generation using Google's Gemini Vision API.

## Features

- **AI-Powered Image Categorization**: Uses Google Gemini 2.0-flash Vision model
- **Multiple Input Formats**: Supports ZIP archives and PDF documents
- **Batch Processing**: Concurrent processing of multiple images
- **PDF Report Generation**: Creates structured PDF reports with categorized images
- **Professional Architecture**: Enterprise-grade folder structure with separation of concerns
- **Async Processing**: Non-blocking file processing with background cleanup
- **Comprehensive Monitoring**: Built-in metrics and health checks
- **Docker Support**: Production-ready containerization
- **Extensive Testing**: Unit tests and integration tests

## Supported Categories

- Address
- Overview
- DC_Disconnect
- Module_label
- Inverter_FrontCover
- Inverter_label
- AC_Disconnect
- PV_Installation
- Battery
- Permit
- Optimizer_Microinverter

## Quick Start

### Prerequisites

- Python 3.9+
- Google Gemini API key

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   ```bash
   export GEMINI_API_KEY=your_api_key_here
   ```

4. Run the application:
   ```bash
   uvicorn app.main:app --reload
   ```

### Using Docker

```bash
docker-compose up --build
```

## API Documentation

Once running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Architecture

```
ai-agent/
├── app/
│   ├── api/           # API routes and endpoints
│   ├── config/        # Configuration management
│   ├── core/          # Core functionality (security, middleware, etc.)
│   ├── models/        # Database models
│   ├── schemas/       # Pydantic schemas
│   ├── services/      # Business logic
│   ├── repositories/  # Data access layer
│   ├── utils/         # Utility functions
│   ├── monitoring/    # Metrics and monitoring
│   └── fallbacks/     # Fallback mechanisms
├── tests/             # Test suite
├── docker/            # Docker configuration
└── docs/              # Documentation
```

## Development

### Running Tests

```bash
pytest
```

### Code Quality

```bash
# Format code
black app/

# Check types
mypy app/

# Lint code
flake8 app/
```

## Configuration

Key configuration options in `app/config/settings.py`:

- `GEMINI_API_KEY`: Google Gemini API key
- `MAX_WORKERS`: Maximum concurrent workers for image processing
- `MAX_FILE_SIZE`: Maximum upload file size
- `IMAGE_MAX_SIZE`: Maximum image dimensions for optimization

## Monitoring

The application includes built-in monitoring:

- Health checks: `/api/v1/health/`
- Metrics: Available through the monitoring module
- Request/response logging
- Processing time tracking
- Token usage monitoring

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

[Your License Here]
