"""
Agent database model
"""

from sqlalchemy import Column, String, Text, <PERSON><PERSON>an, JSON
from app.models.base import BaseModel

class Agent(BaseModel):
    """Agent model"""
    __tablename__ = "agents"
    
    name = Column(String, nullable=False)
    description = Column(Text)
    model_name = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    
    # Configuration stored as JSON
    configuration = Column(JSON)
    
    # Capabilities
    capabilities = Column(JSON)  # List of capabilities
    supported_file_types = Column(JSON)  # List of supported file types
