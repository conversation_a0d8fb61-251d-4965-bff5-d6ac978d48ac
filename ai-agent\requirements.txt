# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Async file handling
aiofiles==23.2.1

# Image processing
Pillow==10.1.0

# PDF generation
reportlab==4.0.7

# PDF processing
PyMuPDF==1.23.8

# HTTP requests
requests==2.31.0

# Environment variables
python-dotenv==1.0.0

# Data validation
pydantic[email]==2.5.0
pydantic-settings==2.1.0

# Database (SQLAlchemy)
sqlalchemy==2.0.23
alembic==1.13.1

# Security
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.6

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Code quality
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Logging
structlog==23.2.0

# Monitoring
psutil==5.9.6

# Development
watchfiles==0.21.0
