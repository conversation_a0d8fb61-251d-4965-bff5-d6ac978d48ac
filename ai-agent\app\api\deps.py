"""
API Dependencies
"""

from fastapi import Depends, HTTPException, status, UploadFile, File
from typing import Optional
import os
from app.config.settings import get_settings
from app.core.dependencies import get_settings_dependency

settings = get_settings()

async def validate_file_upload(
    file: UploadFile = File(...),
    settings = Depends(get_settings_dependency)
) -> UploadFile:
    """Validate uploaded file"""
    
    # Check file size
    if file.size and file.size > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes"
        )
    
    # Check file extension
    if file.filename:
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in settings.ALLOWED_FILE_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type {file_ext} not allowed. Allowed types: {settings.ALLOWED_FILE_TYPES}"
            )
    
    return file

async def get_chunk_size(chunk_size: Optional[int] = None) -> int:
    """Get and validate chunk size parameter"""
    if chunk_size is None:
        return settings.DEFAULT_CHUNK_SIZE
    
    if chunk_size < 1 or chunk_size > settings.MAX_WORKERS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Chunk size must be between 1 and {settings.MAX_WORKERS}"
        )
    
    return chunk_size
