"""
Core Image Processing Module
Contains the main image processing logic for categorization and PDF generation
"""

import os
import zipfile
import tempfile
import logging
import time
from typing import List, Tuple, Optional, Dict
from pathlib import Path
import google.generativeai as genai
from PIL import Image
import fitz  # PyMuPDF
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image as RLImage
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors

from app.config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# Configure Gemini API
if settings.GEMINI_API_KEY:
    genai.configure(api_key=settings.GEMINI_API_KEY)

def find_images(file_path: str, temp_dir: str) -> List[str]:
    """
    Extract images from ZIP or PDF files
    
    Args:
        file_path: Path to the input file
        temp_dir: Temporary directory for extraction
        
    Returns:
        List of image file paths
    """
    image_paths = []
    file_ext = os.path.splitext(file_path)[1].lower()
    
    try:
        if file_ext == '.zip':
            # Extract ZIP file
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # Find all image files
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        image_paths.append(os.path.join(root, file))
                        
        elif file_ext == '.pdf':
            # Extract images from PDF
            pdf_document = fitz.open(file_path)
            
            for page_num in range(len(pdf_document)):
                page = pdf_document.load_page(page_num)
                image_list = page.get_images()
                
                for img_index, img in enumerate(image_list):
                    xref = img[0]
                    pix = fitz.Pixmap(pdf_document, xref)
                    
                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        img_path = os.path.join(temp_dir, f"page_{page_num}_img_{img_index}.png")
                        pix.save(img_path)
                        image_paths.append(img_path)
                    
                    pix = None
            
            pdf_document.close()
            
    except Exception as e:
        logger.error(f"Error extracting images from {file_path}: {e}")
        
    logger.info(f"Found {len(image_paths)} images")
    return image_paths

def get_image_category(image_path: str) -> Tuple[str, int, int]:
    """
    Categorize an image using Gemini Vision API
    
    Args:
        image_path: Path to the image file
        
    Returns:
        Tuple of (category, input_tokens, output_tokens)
    """
    try:
        # Load and prepare image
        image = Image.open(image_path)
        
        # Convert to RGB if necessary
        if image.mode in ('RGBA', 'LA', 'P'):
            image = image.convert('RGB')
        
        # Resize if too large
        max_size = (1024, 1024)
        image.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        # Save optimized image
        temp_path = image_path + "_optimized.jpg"
        image.save(temp_path, 'JPEG', quality=85, optimize=True)
        
        # Prepare the model
        model = genai.GenerativeModel(settings.GEMINI_MODEL)
        
        # Prepare the prompt
        prompt = """
        Analyze this image and categorize it into one of these categories:
        - Address
        - Overview  
        - DC_Disconnect
        - Module_label
        - Inverter_FrontCover
        - Inverter_label
        - AC_Disconnect
        - PV_Installation
        - Battery
        - Permit
        - Optimizer_Microinverter
        
        Return only the category name, nothing else.
        """
        
        # Load the optimized image
        with open(temp_path, 'rb') as f:
            image_data = f.read()
        
        # Generate content
        response = model.generate_content([prompt, {"mime_type": "image/jpeg", "data": image_data}])
        
        # Clean up temporary file
        if os.path.exists(temp_path):
            os.remove(temp_path)
        
        # Extract category from response
        category = response.text.strip() if response.text else "Unknown"
        
        # Get token usage (if available)
        input_tokens = getattr(response.usage_metadata, 'prompt_token_count', 0) if hasattr(response, 'usage_metadata') else 0
        output_tokens = getattr(response.usage_metadata, 'candidates_token_count', 0) if hasattr(response, 'usage_metadata') else 0
        
        logger.info(f"Categorized {os.path.basename(image_path)} as: {category}")
        return category, input_tokens, output_tokens
        
    except Exception as e:
        logger.error(f"Error categorizing image {image_path}: {e}")
        return "Unknown", 0, 0

def generate_pdf_report(categorized_images: Dict[str, List[str]], output_path: str) -> str:
    """
    Generate PDF report with categorized images
    
    Args:
        categorized_images: Dictionary mapping categories to image paths
        output_path: Path for the output PDF
        
    Returns:
        Path to the generated PDF
    """
    try:
        doc = SimpleDocTemplate(output_path, pagesize=letter)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.darkblue,
            alignment=1  # Center alignment
        )
        story.append(Paragraph("Image Categorization Report", title_style))
        story.append(Spacer(1, 20))
        
        # Process each category
        for category, image_paths in categorized_images.items():
            if not image_paths:
                continue
                
            # Category header
            category_style = ParagraphStyle(
                'CategoryHeader',
                parent=styles['Heading2'],
                fontSize=16,
                spaceAfter=12,
                textColor=colors.darkgreen
            )
            story.append(Paragraph(f"Category: {category} ({len(image_paths)} images)", category_style))
            story.append(Spacer(1, 10))
            
            # Add images
            for img_path in image_paths:
                try:
                    # Add image with proper sizing
                    img = RLImage(img_path, width=4*inch, height=3*inch)
                    story.append(img)
                    story.append(Spacer(1, 10))
                    
                    # Add image filename
                    filename = os.path.basename(img_path)
                    story.append(Paragraph(f"File: {filename}", styles['Normal']))
                    story.append(Spacer(1, 15))
                    
                except Exception as e:
                    logger.warning(f"Could not add image {img_path} to PDF: {e}")
                    continue
            
            story.append(Spacer(1, 20))
        
        # Build PDF
        doc.build(story)
        logger.info(f"PDF report generated: {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"Error generating PDF report: {e}")
        raise

def run_agent(file_path: str, output_path: str, chunk_size: int = 20) -> Tuple[Optional[str], int, int, List[float]]:
    """
    Main processing function - categorizes images and generates PDF report

    Args:
        file_path: Path to input file (ZIP or PDF)
        output_path: Path for output PDF
        chunk_size: Number of images to process in each batch

    Returns:
        Tuple of (generated_pdf_path, total_input_tokens, total_output_tokens, chunk_times)
    """
    logger.info(f"Starting image processing: {file_path}")
    
    temp_dir = tempfile.mkdtemp()
    total_input_tokens = 0
    total_output_tokens = 0
    chunk_times = []
    
    try:
        # Extract images
        image_paths = find_images(file_path, temp_dir)
        
        if not image_paths:
            logger.warning("No images found in the input file")
            return None, 0, 0, []

        logger.info(f"Found {len(image_paths)} images total")
        logger.info(f"Processing in chunks of {chunk_size} images")

        # Categorize images in chunks
        categorized_images = {}

        # Process images in chunks
        for i in range(0, len(image_paths), chunk_size):
            chunk = image_paths[i:i + chunk_size]
            chunk_start_time = time.time()

            logger.info(f"Processing chunk {i//chunk_size + 1}/{(len(image_paths) + chunk_size - 1)//chunk_size} ({len(chunk)} images)")

            for j, image_path in enumerate(chunk):
                logger.info(f"  Processing image {j+1}/{len(chunk)}: {os.path.basename(image_path)}")
                category, input_tokens, output_tokens = get_image_category(image_path)
                total_input_tokens += input_tokens
                total_output_tokens += output_tokens

                if category not in categorized_images:
                    categorized_images[category] = []
                categorized_images[category].append(image_path)

            chunk_end_time = time.time()
            chunk_time = chunk_end_time - chunk_start_time
            chunk_times.append(chunk_time)

            logger.info(f"Chunk {i//chunk_size + 1} completed in {chunk_time:.2f} seconds")
        
        # Generate PDF report
        if categorized_images:
            pdf_path = generate_pdf_report(categorized_images, output_path)
            logger.info(f"Processing completed successfully. PDF: {pdf_path}")
            return pdf_path, total_input_tokens, total_output_tokens, chunk_times
        else:
            logger.warning("No images were successfully categorized")
            return None, total_input_tokens, total_output_tokens, chunk_times
            
    except Exception as e:
        logger.error(f"Error in run_agent: {e}")
        return None, total_input_tokens, total_output_tokens, chunk_times
        
    finally:
        # Cleanup temp directory
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir, ignore_errors=True)
