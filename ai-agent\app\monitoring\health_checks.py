"""
Health check endpoints and monitoring
"""

from fastapi import APIRouter
from app.config.settings import get_settings

health_router = APIRouter()
settings = get_settings()

@health_router.get("/")
async def health_check():
    """Basic health check endpoint"""
    api_key_set = bool(settings.GEMINI_API_KEY)
    return {
        "status": "healthy",
        "service": settings.PROJECT_NAME,
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "gemini_api_key_configured": api_key_set,
        "dependencies": {
            "fastapi": "✓",
            "gemini_api": "✓" if api_key_set else "✗ API key not configured",
            "image_processing": "✓",
            "pdf_generation": "✓"
        }
    }

@health_router.get("/detailed")
async def detailed_health_check():
    """Detailed health check with system information"""
    api_key_set = bool(settings.GEMINI_API_KEY)
    
    return {
        "status": "healthy",
        "service": settings.PROJECT_NAME,
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "configuration": {
            "host": settings.HOST,
            "port": settings.PORT,
            "debug": settings.DEBUG,
            "max_file_size": settings.MAX_FILE_SIZE,
            "max_workers": settings.MAX_WORKERS,
            "allowed_file_types": settings.ALLOWED_FILE_TYPES
        },
        "ai_service": {
            "model": settings.GEMINI_MODEL,
            "api_key_configured": api_key_set
        },
        "image_processing": {
            "max_size": settings.IMAGE_MAX_SIZE,
            "quality": settings.IMAGE_QUALITY
        },
        "dependencies": {
            "fastapi": "✓",
            "gemini_api": "✓" if api_key_set else "✗ API key not configured",
            "image_processing": "✓",
            "pdf_generation": "✓",
            "file_processing": "✓"
        }
    }
