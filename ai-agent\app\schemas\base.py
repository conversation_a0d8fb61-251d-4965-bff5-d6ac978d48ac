"""
Base Pydantic schemas
"""

from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class BaseSchema(BaseModel):
    """Base schema with common fields"""
    id: str
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True, "protected_namespaces": ()}

class BaseCreateSchema(BaseModel):
    """Base schema for create operations"""
    pass

class BaseUpdateSchema(BaseModel):
    """Base schema for update operations"""
    pass
