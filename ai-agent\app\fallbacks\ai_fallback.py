"""
AI service fallback mechanisms
"""

import logging
from typing import Optional, Dict, Any
from app.core.exceptions import AIServiceError

logger = logging.getLogger(__name__)

class AIFallbackHandler:
    """Handle AI service failures with fallback mechanisms"""
    
    def __init__(self):
        self.fallback_categories = [
            "Address", "Overview", "DC_Disconnect", "Module_label",
            "Inverter_FrontCover", "Inverter_label", "AC_Disconnect",
            "PV_Installation", "Battery", "Permit", "Optimizer_Microinverter"
        ]
    
    def get_fallback_category(self, image_path: str) -> str:
        """
        Provide a fallback category when AI service fails
        
        This is a simple fallback that categorizes based on filename patterns
        In a production system, this could be more sophisticated
        """
        filename = image_path.lower()
        
        # Simple pattern matching based on filename
        if any(word in filename for word in ['address', 'addr']):
            return "Address"
        elif any(word in filename for word in ['overview', 'general', 'main']):
            return "Overview"
        elif any(word in filename for word in ['disconnect', 'dc']):
            return "DC_Disconnect"
        elif any(word in filename for word in ['module', 'panel']):
            return "Module_label"
        elif any(word in filename for word in ['inverter', 'inv']):
            return "Inverter_FrontCover"
        elif any(word in filename for word in ['ac']):
            return "AC_Disconnect"
        elif any(word in filename for word in ['installation', 'install', 'pv']):
            return "PV_Installation"
        elif any(word in filename for word in ['battery', 'batt']):
            return "Battery"
        elif any(word in filename for word in ['permit', 'license']):
            return "Permit"
        elif any(word in filename for word in ['optimizer', 'micro']):
            return "Optimizer_Microinverter"
        else:
            # Default fallback category
            return "Overview"
    
    def handle_ai_service_failure(self, error: Exception, image_path: str) -> Dict[str, Any]:
        """
        Handle AI service failure and provide fallback response
        
        Args:
            error: The exception that occurred
            image_path: Path to the image that failed to process
            
        Returns:
            Fallback response with category and metadata
        """
        logger.warning(f"AI service failed for image {image_path}: {error}")
        logger.info(f"Using fallback categorization for {image_path}")
        
        fallback_category = self.get_fallback_category(image_path)
        
        return {
            "category": fallback_category,
            "confidence": 0.1,  # Low confidence for fallback
            "fallback": True,
            "error": str(error),
            "method": "filename_pattern_matching"
        }
    
    def is_service_available(self) -> bool:
        """
        Check if AI service is available
        This could include health checks, API key validation, etc.
        """
        # Placeholder implementation
        # In a real system, this would check API connectivity, rate limits, etc.
        return True
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get AI service status information"""
        return {
            "available": self.is_service_available(),
            "fallback_enabled": True,
            "fallback_categories": self.fallback_categories,
            "fallback_method": "filename_pattern_matching"
        }

# Global fallback handler instance
ai_fallback_handler = AIFallbackHandler()

def get_ai_fallback_handler() -> AIFallbackHandler:
    """Get the global AI fallback handler"""
    return ai_fallback_handler
