"""
Streamlit Frontend for AI Agent Image Categorization API
"""

import streamlit as st
import requests
import json
import time
import os
from typing import Optional, Dict, Any
import tempfile
from pathlib import Path
from config import config

def check_api_health() -> bool:
    """Check if the API is healthy and accessible"""
    try:
        response = requests.get(config.ENDPOINTS["health"], timeout=config.API_HEALTH_TIMEOUT)
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False

def get_agent_capabilities() -> Optional[Dict[str, Any]]:
    """Get agent capabilities from the API"""
    try:
        response = requests.get(config.ENDPOINTS["capabilities"], timeout=config.API_CAPABILITIES_TIMEOUT)
        if response.status_code == 200:
            return response.json()
        return None
    except requests.exceptions.RequestException:
        return None

def process_file(file_path: str, chunk_size: int = None) -> Dict[str, Any]:
    """Send file to API for processing"""
    if chunk_size is None:
        chunk_size = config.DEFAULT_CHUNK_SIZE

    try:
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {'chunk_size': chunk_size}

            response = requests.post(
                config.ENDPOINTS["process"],
                files=files,
                data=data,
                timeout=config.API_PROCESS_TIMEOUT
            )
            
        if response.status_code == 200:
            return response.json()
        else:
            return {
                "success": False,
                "error_message": f"API Error: {response.status_code} - {response.text}"
            }
            
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "error_message": f"Request failed: {str(e)}"
        }

def main():
    """Main Streamlit application"""
    
    # Page configuration
    st.set_page_config(
        page_title=config.PAGE_TITLE,
        page_icon=config.PAGE_ICON,
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Header
    st.title("🖼️ AI Image Categorization & PDF Generation")
    st.markdown("Upload ZIP archives or PDF documents to categorize images using AI and generate organized PDF reports.")
    
    # Sidebar - API Status and Configuration
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # API Health Check
        st.subheader("🔗 API Status")
        if check_api_health():
            st.success("✅ API is healthy")
            
            # Get capabilities
            capabilities = get_agent_capabilities()
            if capabilities:
                st.info(f"🤖 Model: {capabilities.get('model', 'Unknown')}")
                st.info(f"📊 Max Workers: {capabilities.get('max_workers', 'Unknown')}")
        else:
            st.error("❌ API is not accessible")
            st.warning(f"Please ensure the FastAPI server is running on {config.API_BASE_URL}")
            return
        
        # Processing Configuration
        st.subheader("🔧 Processing Settings")
        chunk_size = st.slider(
            "Chunk Size (images per batch)",
            min_value=1,
            max_value=50,
            value=20,
            help="Number of images to process in each batch. Smaller chunks = more frequent progress updates."
        )
        
        # Supported File Types
        if capabilities:
            st.subheader("📁 Supported Files")
            supported_types = capabilities.get('supported_file_types', [])
            for file_type in supported_types:
                st.text(f"• {file_type}")
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("📤 Upload File")
        
        # File uploader
        uploaded_file = st.file_uploader(
            "Choose a ZIP archive or PDF document",
            type=config.SUPPORTED_FILE_TYPES,
            help=f"Upload a ZIP file containing images or a PDF document with embedded images (Max size: {config.MAX_FILE_SIZE_MB} MB)"
        )
        
        if uploaded_file is not None:
            # Display file info
            file_size_mb = uploaded_file.size / (1024 * 1024)
            st.success(f"✅ File uploaded: {uploaded_file.name}")
            st.info(f"📏 File size: {file_size_mb:.2f} MB ({uploaded_file.size:,} bytes)")

            # Check file size limit
            if file_size_mb > config.MAX_FILE_SIZE_MB:
                st.error(f"❌ File too large! Maximum allowed size is {config.MAX_FILE_SIZE_MB} MB")
                return
            
            # Process button
            if st.button("🚀 Start Processing", type="primary"):
                # Save uploaded file temporarily
                with tempfile.NamedTemporaryFile(delete=False, suffix=Path(uploaded_file.name).suffix) as tmp_file:
                    tmp_file.write(uploaded_file.getvalue())
                    temp_file_path = tmp_file.name
                
                try:
                    # Processing UI
                    st.header("⚡ Processing")
                    
                    # Progress indicators
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    status_text.text("🔄 Sending file to AI agent...")
                    progress_bar.progress(10)
                    
                    # Call API
                    start_time = time.time()
                    result = process_file(temp_file_path, chunk_size)
                    end_time = time.time()
                    
                    progress_bar.progress(100)
                    
                    if result.get("success", False):
                        status_text.text("✅ Processing completed successfully!")
                        
                        # Display results
                        st.header("📊 Results")
                        
                        # Metrics
                        col_metrics1, col_metrics2, col_metrics3, col_metrics4 = st.columns(4)
                        
                        with col_metrics1:
                            st.metric(
                                "Images Processed",
                                result.get("images_processed", 0)
                            )
                        
                        with col_metrics2:
                            st.metric(
                                "Processing Time",
                                f"{result.get('processing_time', 0):.1f}s"
                            )
                        
                        with col_metrics3:
                            st.metric(
                                "Input Tokens",
                                f"{result.get('input_tokens', 0):,}"
                            )
                        
                        with col_metrics4:
                            st.metric(
                                "Output Tokens",
                                f"{result.get('output_tokens', 0):,}"
                            )
                        
                        # Download link for generated PDF
                        if result.get("output_file_path"):
                            st.success("📄 PDF report generated successfully!")
                            st.info(f"📁 Output file: {result['output_file_path']}")
                            
                            # Note about file location
                            st.warning("💡 The generated PDF is saved on the server. In a production environment, you would implement file download functionality.")
                    
                    else:
                        status_text.text("❌ Processing failed")
                        st.error(f"Error: {result.get('error_message', 'Unknown error')}")
                
                finally:
                    # Cleanup temporary file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)
    
    with col2:
        st.header("ℹ️ Information")
        
        # Categories
        st.subheader("🏷️ Image Categories")
        categories = [
            "Address", "Overview", "DC_Disconnect", "Module_label",
            "Inverter_FrontCover", "Inverter_label", "AC_Disconnect",
            "PV_Installation", "Battery", "Permit", "Optimizer_Microinverter"
        ]
        
        for category in categories:
            st.text(f"• {category}")
        
        # Instructions
        st.subheader("📋 Instructions")
        st.markdown("""
        1. **Upload** a ZIP file with images or a PDF document
        2. **Configure** chunk size for processing
        3. **Click** "Start Processing" to begin
        4. **Wait** for the AI to categorize images
        5. **Download** the generated PDF report
        """)
        
        # Tips
        st.subheader("💡 Tips")
        st.markdown("""
        - **Smaller chunk sizes** provide more frequent progress updates
        - **Larger chunk sizes** may process faster overall
        - **ZIP files** should contain image files (JPG, PNG, etc.)
        - **PDF files** will have images extracted automatically
        """)

if __name__ == "__main__":
    main()
