#!/usr/bin/env python3
"""
Basic functionality test for the restructured AI Agent application
"""

import sys
import os

def test_imports():
    """Test that all core modules can be imported"""
    print("Testing imports...")
    
    try:
        from app.config.settings import get_settings
        print("✓ Settings import successful")
        
        settings = get_settings()
        print(f"✓ Settings loaded: {settings.PROJECT_NAME}")
        
        from app.services.agent_service import AgentService
        print("✓ AgentService import successful")
        
        service = AgentService()
        print("✓ AgentService instantiation successful")
        
        from app.schemas.agent import ProcessingRequest, ProcessingResult
        print("✓ Schema imports successful")
        
        # Test schema creation
        request = ProcessingRequest(chunk_size=10)
        print(f"✓ ProcessingRequest created: chunk_size={request.chunk_size}")
        
        result = ProcessingResult(
            success=True,
            processing_time=1.5,
            images_processed=5
        )
        print(f"✓ ProcessingResult created: success={result.success}")
        
        return True
        
    except Exception as e:
        print(f"✗ Import test failed: {e}")
        return False

def test_core_processing():
    """Test that core processing functions are accessible"""
    print("\nTesting core processing functions...")
    
    try:
        # Add the fastapi_migration directory to path
        sys.path.append('../fastapi_migration')
        
        from main import run_agent, find_images, get_image_category
        print("✓ Core processing functions imported successfully")
        
        # Test that functions exist and are callable
        assert callable(run_agent), "run_agent should be callable"
        assert callable(find_images), "find_images should be callable"
        assert callable(get_image_category), "get_image_category should be callable"
        print("✓ Core functions are callable")
        
        return True
        
    except Exception as e:
        print(f"✗ Core processing test failed: {e}")
        return False

def test_directory_structure():
    """Test that the directory structure is correct"""
    print("\nTesting directory structure...")
    
    required_dirs = [
        "app",
        "app/api",
        "app/api/v1",
        "app/api/v1/endpoints",
        "app/config",
        "app/core",
        "app/models",
        "app/schemas",
        "app/services",
        "app/repositories",
        "app/utils",
        "app/monitoring",
        "app/fallbacks",
        "tests",
        "docker",
        "docs"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"✗ Missing directories: {missing_dirs}")
        return False
    else:
        print("✓ All required directories exist")
        return True

def test_configuration_files():
    """Test that configuration files exist"""
    print("\nTesting configuration files...")
    
    required_files = [
        "requirements.txt",
        "pyproject.toml",
        ".env.example",
        ".gitignore",
        "alembic.ini"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"✗ Missing configuration files: {missing_files}")
        return False
    else:
        print("✓ All required configuration files exist")
        return True

def main():
    """Run all tests"""
    print("=" * 60)
    print("AI Agent - Basic Functionality Test")
    print("=" * 60)
    
    tests = [
        test_directory_structure,
        test_configuration_files,
        test_imports,
        test_core_processing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The restructured application is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
