"""
Agents API endpoints - Main image processing functionality
"""

import os
import tempfile
import shutil
import asyncio
import logging
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.responses import FileResponse
import aiofiles

from app.api.deps import validate_file_upload, get_chunk_size
from app.schemas.agent import ProcessingResponse, ProcessingRequest
from app.services.agent_service import AgentService, get_agent_service
from app.core.exceptions import ImageProcessingError, AIServiceError, FileProcessingError
from app.config.settings import get_settings

router = APIRouter()
logger = logging.getLogger(__name__)
settings = get_settings()

async def cleanup_temp_directory(temp_dir: str):
    """Background task to clean up temporary directory"""
    try:
        if temp_dir and os.path.exists(temp_dir):
            logger.info(f"Cleaning up temporary directory: {temp_dir}")
            shutil.rmtree(temp_dir, ignore_errors=True)
        else:
            logger.info(f"No temporary directory to clean up or it was already removed: {temp_dir}")
    except Exception as e:
        logger.error(f"Error cleaning up temporary directory {temp_dir}: {e}")

@router.get("/")
async def get_agents():
    """Get available agents information"""
    return {
        "message": "Image Categorization and PDF Generation Agent",
        "version": "2.0.0",
        "description": "AI-powered image categorization and PDF report generation",
        "capabilities": [
            "ZIP archive processing",
            "PDF document processing", 
            "Image categorization using Gemini Vision",
            "PDF report generation",
            "Batch processing support"
        ],
        "supported_formats": {
            "input": ["ZIP archives", "PDF documents"],
            "images": ["JPG", "JPEG", "PNG"],
            "output": "PDF report with categorized images"
        },
        "ai_model": "Google Gemini 2.0-flash Vision",
        "supported_categories": [
            "Address", "Overview", "DC_Disconnect", "Module_label",
            "Inverter_FrontCover", "Inverter_label", "AC_Disconnect", 
            "PV_Installation", "Battery", "Permit", "Optimizer_Microinverter"
        ]
    }

@router.post("/process")
async def process_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = Depends(validate_file_upload),
    chunk_size: int = Depends(get_chunk_size),
    agent_service: AgentService = Depends(get_agent_service)
):
    """
    Process uploaded ZIP or PDF file containing images.
    
    This endpoint:
    1. Accepts ZIP archives or PDF documents
    2. Extracts images from the uploaded file
    3. Categorizes images using Google's Gemini Vision API
    4. Generates a PDF report with categorized images
    5. Returns the generated PDF file
    
    **Supported file types:** .zip, .pdf
    **Supported image formats:** JPG, JPEG, PNG
    """
    
    logger.info(f"Processing uploaded file: {file.filename}")
    logger.info(f"File content type: {file.content_type}")
    logger.info(f"Chunk size: {chunk_size}")
    
    # Check API key
    if not settings.GEMINI_API_KEY:
        raise HTTPException(
            status_code=500,
            detail="GEMINI_API_KEY environment variable is not set. Please configure the API key."
        )
    
    temp_dir = None
    try:
        # Create temporary directory for processing
        temp_dir = tempfile.mkdtemp()
        logger.info(f"Created temporary directory: {temp_dir}")
        
        # Save uploaded file
        file_path = os.path.join(temp_dir, file.filename)
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        logger.info(f"Saved uploaded file to: {file_path}")
        
        # Define output PDF path
        output_pdf_name = "categorized_images.pdf"
        output_pdf_path = os.path.join(temp_dir, output_pdf_name)
        
        # Process the file using the agent service
        result = await agent_service.process_file(
            file_path=file_path,
            output_path=output_pdf_path,
            chunk_size=chunk_size
        )
        
        if result.success and result.output_file_path and os.path.exists(result.output_file_path):
            logger.info(f"PDF generated successfully: {result.output_file_path}")
            logger.info(f"Token usage - Input: {result.input_tokens}, Output: {result.output_tokens}")
            
            # Schedule cleanup of temporary directory after response is sent
            background_tasks.add_task(cleanup_temp_directory, temp_dir)
            
            # Return the generated PDF
            return FileResponse(
                path=result.output_file_path,
                filename=output_pdf_name,
                media_type="application/pdf",
                headers={
                    "Content-Disposition": f"attachment; filename={output_pdf_name}",
                    "X-Input-Tokens": str(result.input_tokens),
                    "X-Output-Tokens": str(result.output_tokens),
                    "X-Processing-Time": str(result.processing_time),
                    "X-Images-Processed": str(result.images_processed)
                }
            )
        else:
            # Clean up immediately if no PDF was generated
            if temp_dir and os.path.exists(temp_dir):
                logger.info(f"Cleaning up temporary directory immediately (no PDF generated): {temp_dir}")
                shutil.rmtree(temp_dir, ignore_errors=True)
            
            error_message = result.error_message if result.error_message else "No images found or could be categorized"
            raise HTTPException(
                status_code=400,
                detail=f"Processing failed: {error_message}. Please ensure your file contains valid images (JPG, JPEG, PNG) or PDFs with embedded images."
            )
    
    except HTTPException:
        # Re-raise HTTP exceptions
        if temp_dir and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir, ignore_errors=True)
        raise
    except (ImageProcessingError, AIServiceError, FileProcessingError) as e:
        # Handle custom exceptions
        logger.error(f"Processing error for file {file.filename}: {e}")
        if temp_dir and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir, ignore_errors=True)
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        # Handle unexpected errors
        logger.error(f"Unexpected error processing file {file.filename}: {e}")
        if temp_dir and os.path.exists(temp_dir):
            shutil.rmtree(temp_dir, ignore_errors=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error occurred while processing the file: {str(e)}"
        )

@router.get("/status")
async def get_agent_status():
    """Get agent status and configuration"""
    return {
        "status": "active",
        "ai_model": settings.GEMINI_MODEL,
        "api_key_configured": bool(settings.GEMINI_API_KEY),
        "max_workers": settings.MAX_WORKERS,
        "default_chunk_size": settings.DEFAULT_CHUNK_SIZE,
        "max_file_size": settings.MAX_FILE_SIZE,
        "allowed_file_types": settings.ALLOWED_FILE_TYPES,
        "image_processing": {
            "max_size": settings.IMAGE_MAX_SIZE,
            "quality": settings.IMAGE_QUALITY
        }
    }
