"""
Message database model
"""

from sqlalchemy import Column, String, Text, ForeignKey, Integer
from sqlalchemy.orm import relationship
from app.models.base import BaseModel

class Message(BaseModel):
    """Message model"""
    __tablename__ = "messages"
    
    content = Column(Text, nullable=False)
    role = Column(String, nullable=False)  # 'user', 'assistant', 'system'
    conversation_id = Column(String, ForeignKey("conversations.id"), nullable=False)
    sequence_number = Column(Integer, nullable=False)
    
    # Token usage tracking
    input_tokens = Column(Integer, default=0)
    output_tokens = Column(Integer, default=0)
    
    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
