# PowerShell script to start both FastAPI backend and Streamlit frontend

Write-Host "🚀 Starting AI Agent Services..." -ForegroundColor Green

# Start FastAPI backend in background
Write-Host "📡 Starting FastAPI backend on port 8000..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-Command", "cd '$PSScriptRoot'; uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload" -WindowStyle Normal

# Wait a moment for FastAPI to start
Start-Sleep -Seconds 3

# Start Streamlit frontend
Write-Host "🖥️ Starting Streamlit frontend on port 8501..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-Command", "cd '$PSScriptRoot\streamlit_frontend'; streamlit run app.py" -WindowStyle Normal

Write-Host "✅ Services started!" -ForegroundColor Green
Write-Host "📡 FastAPI Backend: http://localhost:8000" -ForegroundColor Cyan
Write-Host "🖥️ Streamlit Frontend: http://localhost:8501" -ForegroundColor Cyan
Write-Host "📚 API Documentation: http://localhost:8000/docs" -ForegroundColor Cyan

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
