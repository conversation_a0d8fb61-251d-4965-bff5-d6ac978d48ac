"""
Conversation Repository
"""

from typing import List
from sqlalchemy.orm import Session, joinedload
from app.models.conversation import Conversation
from app.repositories.base import BaseRepository

class ConversationRepository(BaseRepository[Conversation]):
    """Repository for conversation data access"""
    
    def __init__(self, db: Session):
        super().__init__(Conversation, db)
    
    def get_by_user(self, user_id: str, skip: int = 0, limit: int = 100) -> List[Conversation]:
        """Get conversations by user ID"""
        return (
            self.db.query(Conversation)
            .filter(Conversation.user_id == user_id)
            .order_by(Conversation.updated_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_with_messages(self, conversation_id: str) -> Conversation:
        """Get conversation with messages loaded"""
        return (
            self.db.query(Conversation)
            .options(joinedload(Conversation.messages))
            .filter(Conversation.id == conversation_id)
            .first()
        )
    
    def get_recent_conversations(self, limit: int = 10) -> List[Conversation]:
        """Get recent conversations across all users"""
        return (
            self.db.query(Conversation)
            .order_by(Conversation.updated_at.desc())
            .limit(limit)
            .all()
        )
