"""
Image utility functions
"""

import os
from PIL import Image
from typing import <PERSON>ple, Optional

def optimize_image(image_path: str, max_size: Tuple[int, int] = (1024, 1024), quality: int = 85) -> str:
    """
    Optimize image by resizing and compressing
    
    Args:
        image_path: Path to the image file
        max_size: Maximum dimensions (width, height)
        quality: JPEG quality (1-100)
    
    Returns:
        Path to the optimized image (same as input)
    """
    try:
        with Image.open(image_path) as img:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # Resize if larger than max_size
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # Save with optimization
            img.save(image_path, 'JPEG', quality=quality, optimize=True)
        
        return image_path
    except Exception as e:
        print(f"Error optimizing image {image_path}: {e}")
        return image_path

def get_image_dimensions(image_path: str) -> Optional[Tuple[int, int]]:
    """Get image dimensions"""
    try:
        with Image.open(image_path) as img:
            return img.size
    except Exception:
        return None

def is_valid_image(image_path: str) -> bool:
    """Check if file is a valid image"""
    try:
        with Image.open(image_path) as img:
            img.verify()
        return True
    except Exception:
        return False

def convert_to_jpeg(image_path: str, output_path: Optional[str] = None) -> str:
    """Convert image to JPEG format"""
    if output_path is None:
        base_name = os.path.splitext(image_path)[0]
        output_path = f"{base_name}.jpg"
    
    try:
        with Image.open(image_path) as img:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            img.save(output_path, 'JPEG', quality=90)
        
        return output_path
    except Exception as e:
        print(f"Error converting image to JPEG {image_path}: {e}")
        return image_path
