"""
Agent Repository
"""

from typing import List
from sqlalchemy.orm import Session
from app.models.agent import Agent
from app.repositories.base import BaseRepository

class AgentRepository(BaseRepository[Agent]):
    """Repository for agent data access"""
    
    def __init__(self, db: Session):
        super().__init__(Agent, db)
    
    def get_active_agents(self, skip: int = 0, limit: int = 100) -> List[Agent]:
        """Get active agents"""
        return (
            self.db.query(Agent)
            .filter(Agent.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_model(self, model_name: str) -> List[Agent]:
        """Get agents by model name"""
        return (
            self.db.query(Agent)
            .filter(Agent.model_name == model_name)
            .all()
        )
    
    def get_by_capability(self, capability: str) -> List[Agent]:
        """Get agents by capability"""
        return (
            self.db.query(Agent)
            .filter(Agent.capabilities.contains([capability]))
            .all()
        )
