"""
File utility functions
"""

import os
import tempfile
import shutil
import mimetypes
from typing import List, Optional
from pathlib import Path

def create_temp_directory() -> str:
    """Create a temporary directory"""
    return tempfile.mkdtemp()

def cleanup_temp_directory(temp_dir: str) -> None:
    """Clean up a temporary directory"""
    if temp_dir and os.path.exists(temp_dir):
        shutil.rmtree(temp_dir, ignore_errors=True)

def get_file_extension(filename: str) -> str:
    """Get file extension from filename"""
    return Path(filename).suffix.lower()

def get_mime_type(filename: str) -> Optional[str]:
    """Get MIME type from filename"""
    mime_type, _ = mimetypes.guess_type(filename)
    return mime_type

def is_valid_file_type(filename: str, allowed_types: List[str]) -> bool:
    """Check if file type is allowed"""
    extension = get_file_extension(filename)
    return extension in allowed_types

def get_file_size(file_path: str) -> int:
    """Get file size in bytes"""
    return os.path.getsize(file_path)

def ensure_directory_exists(directory: str) -> None:
    """Ensure directory exists, create if it doesn't"""
    os.makedirs(directory, exist_ok=True)

def safe_filename(filename: str) -> str:
    """Create a safe filename by removing/replacing unsafe characters"""
    # Remove or replace unsafe characters
    unsafe_chars = '<>:"/\\|?*'
    safe_name = filename
    for char in unsafe_chars:
        safe_name = safe_name.replace(char, '_')
    return safe_name

def get_unique_filename(directory: str, filename: str) -> str:
    """Get a unique filename in the given directory"""
    base_name = Path(filename).stem
    extension = Path(filename).suffix
    counter = 1
    
    new_filename = filename
    while os.path.exists(os.path.join(directory, new_filename)):
        new_filename = f"{base_name}_{counter}{extension}"
        counter += 1
    
    return new_filename
