"""
Pytest configuration and fixtures
"""

import pytest
import tempfile
import os
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.config.database import get_db
from app.models.base import Base

# Test database URL (SQLite in memory)
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture
def db_session():
    """Create a test database session"""
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)

@pytest.fixture
def client(db_session):
    """Create a test client"""
    def override_get_db():
        try:
            yield db_session
        finally:
            db_session.close()
    
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()

@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    # Cleanup is handled by the OS for temp directories

@pytest.fixture
def sample_image_file():
    """Create a sample image file for testing"""
    # This would create a simple test image
    # For now, just return a path that tests can use
    return "test_image.jpg"

@pytest.fixture
def sample_zip_file():
    """Create a sample ZIP file for testing"""
    # This would create a test ZIP file with images
    # For now, just return a path that tests can use
    return "test_archive.zip"
