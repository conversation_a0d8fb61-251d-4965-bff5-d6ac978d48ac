"""
Main API v1 Router
"""

from fastapi import APIRouter
from app.api.v1.endpoints import agents, conversations, users, health

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(
    agents.router,
    prefix="/agents",
    tags=["agents"]
)

api_router.include_router(
    conversations.router,
    prefix="/conversations", 
    tags=["conversations"]
)

api_router.include_router(
    users.router,
    prefix="/users",
    tags=["users"]
)

api_router.include_router(
    health.router,
    prefix="/health",
    tags=["health"]
)
