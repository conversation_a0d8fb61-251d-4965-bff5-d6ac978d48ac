"""
Configuration for Streamlit Frontend
"""

import os
from typing import List

class Config:
    """Frontend configuration settings"""
    
    # API Configuration
    API_HOST: str = os.getenv("API_HOST", "************")
    API_PORT: int = int(os.getenv("API_PORT", "8000"))
    API_BASE_URL: str = f"http://{API_HOST}:{API_PORT}"
    
    # API Endpoints
    ENDPOINTS = {
        "process": f"{API_BASE_URL}/v1/agents/process",
        "health": f"{API_BASE_URL}/health",
        "capabilities": f"{API_BASE_URL}/api/v1/agents/capabilities",
        "docs": f"{API_BASE_URL}/docs"
    }
    
    # UI Configuration
    PAGE_TITLE: str = "AI Image Categorization"
    PAGE_ICON: str = "🖼️"
    
    # Processing Configuration
    DEFAULT_CHUNK_SIZE: int = 20
    MIN_CHUNK_SIZE: int = 1
    MAX_CHUNK_SIZE: int = 50
    
    # File Upload Configuration
    SUPPORTED_FILE_TYPES: List[str] = ['zip', 'pdf']
    MAX_FILE_SIZE_MB: int = 500
    
    # Timeouts (seconds)
    API_HEALTH_TIMEOUT: int = 5
    API_CAPABILITIES_TIMEOUT: int = 10
    API_PROCESS_TIMEOUT: int = 300  # 5 minutes
    
    # Image Categories
    IMAGE_CATEGORIES: List[str] = [
        "Address", "Overview", "DC_Disconnect", "Module_label",
        "Inverter_FrontCover", "Inverter_label", "AC_Disconnect",
        "PV_Installation", "Battery", "Permit", "Optimizer_Microinverter"
    ]

# Global config instance
config = Config()
